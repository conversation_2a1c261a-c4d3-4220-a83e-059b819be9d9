<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;51c5d97a-2ec6-47bc-bf88-812ac7e04db5&quot;,&quot;conversations&quot;:{&quot;c0e0706c-8bc6-4c83-a94d-b0c8e99adf4c&quot;:{&quot;id&quot;:&quot;c0e0706c-8bc6-4c83-a94d-b0c8e99adf4c&quot;,&quot;createdAtIso&quot;:&quot;2025-06-07T08:55:29.375Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-07T08:55:29.375Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;428fe6ee-9f5e-452f-8951-e78368314063&quot;},&quot;51c5d97a-2ec6-47bc-bf88-812ac7e04db5&quot;:{&quot;id&quot;:&quot;51c5d97a-2ec6-47bc-bf88-812ac7e04db5&quot;,&quot;createdAtIso&quot;:&quot;2025-06-07T08:55:29.518Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-07T08:55:29.518Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;320b7579-9157-4d22-b8e9-20e653d01899&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;}" />
      </map>
    </option>
  </component>
</project>