---
layout: home

title: Flowbite Vue 3
titleTemplate: Flowbite

hero:
  name: Flowbite Vue 3
  text: Vue component library based on Tailwind CSS
  tagline: Get started with the most popular open-source library of interactive UI components built with the utility classes from Tailwind CSS
  image:
    src: /assets/logo.svg
    alt: VitePress
  actions:
    - theme: brand
      text: Get Started
      link: /pages/getting-started
    - theme: alt
      text: View on GitHub
      link: https://github.com/themesberg/flowbite-vue

features:
  - title: Vue 3 components 🧱
    details: Use hundreds of open-source components such as navbars, modals, and dropdowns based on Vue 3 and Tailwind CSS.
  - title: Based on Tailwind CSS 💨
    details: The components are based on the utility classes from Tailwind CSS and you can use them to further customize the interface.
  - title: Powered by Flowbite 🚀
    details: The Flowbite Vue library is based on the original Flowbite component library using vanilla JavaScript.
  - title: Open-source community ❤️
    details: Thousands of developers actively use the components from Flowbite Vue to power their applications.
---
