<template>
  <div class="vp-raw flex flex-wrap gap-2">
    <fwb-button gradient="purple-blue">
      Purple to blue
    </fwb-button>
    <fwb-button gradient="cyan-blue">
      Cyan to blue
    </fwb-button>
    <fwb-button gradient="green-blue">
      Green to blue
    </fwb-button>
    <fwb-button gradient="purple-pink">
      Purple to pink
    </fwb-button>
    <fwb-button gradient="pink-orange">
      Pink to orange
    </fwb-button>
    <fwb-button gradient="teal-lime">
      Teal to lime
    </fwb-button>
    <fwb-button gradient="red-yellow">
      Red to yellow
    </fwb-button>
  </div>
</template>

<script lang="ts" setup>
import { FwbButton } from '../../../../src/index'
</script>
