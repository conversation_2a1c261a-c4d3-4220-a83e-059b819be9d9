<template>
  <div class="vp-raw flex flex-wrap gap-2">
    <fwb-button
      gradient="purple-blue"
      outline
    >
      Purple to blue
    </fwb-button>
    <fwb-button
      gradient="cyan-blue"
      outline
    >
      Cyan to blue
    </fwb-button>
    <fwb-button
      gradient="green-blue"
      outline
    >
      Green to blue
    </fwb-button>
    <fwb-button
      gradient="purple-pink"
      outline
    >
      Purple to pink
    </fwb-button>
    <fwb-button
      gradient="pink-orange"
      outline
    >
      Pink to orange
    </fwb-button>
    <fwb-button
      gradient="teal-lime"
      outline
    >
      Teal to lime
    </fwb-button>
    <fwb-button
      gradient="red-yellow"
      outline
    >
      Red to yellow
    </fwb-button>
  </div>
</template>

<script lang="ts" setup>
import { FwbButton } from '../../../../src/index'
</script>
