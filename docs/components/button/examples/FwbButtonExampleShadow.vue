<template>
  <div class="vp-raw flex flex-wrap gap-2">
    <fwb-button
      gradient="blue"
      shadow
    >
      Blue with blue
    </fwb-button>
    <fwb-button
      gradient="cyan"
      shadow
    >
      <PERSON><PERSON> with cyan
    </fwb-button>
    <fwb-button
      gradient="green"
      shadow
    >
      Green with green
    </fwb-button>
    <fwb-button
      gradient="lime"
      shadow
    >
      Lime with lime
    </fwb-button>
    <fwb-button
      gradient="pink"
      shadow
    >
      Pink with pink
    </fwb-button>
    <fwb-button
      gradient="purple"
      shadow
    >
      Purple with purple
    </fwb-button>
    <fwb-button
      gradient="red"
      shadow
    >
      Red with red
    </fwb-button>
    <fwb-button
      gradient="teal"
      shadow
    >
      Teal with teal
    </fwb-button>
    <fwb-button
      gradient="blue"
      shadow="red"
    >
      Blue with red
    </fwb-button>
    <fwb-button
      gradient="cyan"
      shadow="teal"
    >
      Cyan with teal
    </fwb-button>
    <fwb-button
      gradient="teal"
      shadow="purple"
    >
      Teal with purple
    </fwb-button>
  </div>
</template>

<script lang="ts" setup>
import { FwbButton } from '../../../../src/index'
</script>
