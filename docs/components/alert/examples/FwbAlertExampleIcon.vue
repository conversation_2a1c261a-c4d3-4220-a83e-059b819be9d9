<template>
  <div class="vp-raw grid gap-2">
    <fwb-alert
      icon
      type="info"
    >
      Info alert! Change a few things up and try submitting again.
    </fwb-alert>
    <fwb-alert
      icon
      type="warning"
    >
      Warning alert! Change a few things up and try submitting again.
    </fwb-alert>
    <fwb-alert
      icon
      type="danger"
    >
      Danger alert! Change a few things up and try submitting again.
    </fwb-alert>
    <fwb-alert
      icon
      type="dark"
    >
      Dark alert! Change a few things up and try submitting again.
    </fwb-alert>
    <fwb-alert
      icon
      type="success"
    >
      Success alert! Change a few things up and try submitting again.
    </fwb-alert>
  </div>
</template>

<script lang="ts" setup>
import { FwbAlert } from '../../../../src/index'
</script>
