<template>
  <div class="vp-raw flex items-end">
    <fwb-badge size="xs">
      Default
    </fwb-badge>
    <fwb-badge
      size="xs"
      type="dark"
    >
      Dark
    </fwb-badge>
    <fwb-badge
      size="xs"
      type="red"
    >
      Red
    </fwb-badge>
    <fwb-badge
      size="xs"
      type="green"
    >
      Green
    </fwb-badge>
    <fwb-badge
      size="sm"
      type="yellow"
    >
      Yellow
    </fwb-badge>
    <fwb-badge
      size="sm"
      type="indigo"
    >
      Indigo
    </fwb-badge>
    <fwb-badge
      size="sm"
      type="purple"
    >
      Purple
    </fwb-badge>
    <fwb-badge
      size="sm"
      type="pink"
    >
      Pink
    </fwb-badge>
  </div>
</template>

<script lang="ts" setup>
import { FwbBadge } from '../../../../src/index'
</script>
