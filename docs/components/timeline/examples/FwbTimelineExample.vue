<template>
  <div class="vp-raw">
    <fwb-timeline>
      <fwb-timeline-item>
        <fwb-timeline-point />
        <fwb-timeline-content>
          <fwb-timeline-time>
            February 2020
          </fwb-timeline-time>
          <fwb-timeline-title>
            Application UI code in Tailwind CSS
          </fwb-timeline-title>
          <fwb-timeline-body>
            Get access to over 20+ pages including a dashboard layout, charts, kanban board, calendar, and pre-order E-commerce & Marketing pages.
          </fwb-timeline-body>
        </fwb-timeline-content>
      </fwb-timeline-item>
      <fwb-timeline-item>
        <fwb-timeline-point />
        <fwb-timeline-content>
          <fwb-timeline-time>
            February 2020
          </fwb-timeline-time>
          <fwb-timeline-title>
            Application UI code in Tailwind CSS
          </fwb-timeline-title>
          <fwb-timeline-body>
            Get access to over 20+ pages including a dashboard layout, charts, kanban board, calendar, and pre-order E-commerce & Marketing pages.
          </fwb-timeline-body>
        </fwb-timeline-content>
      </fwb-timeline-item>
      <fwb-timeline-item>
        <fwb-timeline-point />
        <fwb-timeline-content>
          <fwb-timeline-time>
            February 2020
          </fwb-timeline-time>
          <fwb-timeline-title>
            Application UI code in Tailwind CSS
          </fwb-timeline-title>
          <fwb-timeline-body>
            Get access to over 20+ pages including a dashboard layout, charts, kanban board, calendar, and pre-order E-commerce & Marketing pages.
          </fwb-timeline-body>
        </fwb-timeline-content>
      </fwb-timeline-item>
    </fwb-timeline>
  </div>
</template>

<script lang="ts" setup>
import {
  FwbTimeline,
  FwbTimelineBody,
  FwbTimelineContent,
  FwbTimelineItem,
  FwbTimelinePoint,
  FwbTimelineTime,
  FwbTimelineTitle,
} from '../../../../src/index'
</script>
