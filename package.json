{"name": "flowbite-vue", "version": "0.2.1", "repository": {"type": "git", "url": "git+https://github.com/themesberg/flowbite-vue.git"}, "author": "<PERSON><PERSON>", "license": "MIT", "files": ["dist", "src"], "type": "module", "main": "./dist/flowbite-vue.umd.cjs", "module": "./dist/flowbite-vue.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/flowbite-vue.js", "require": "./dist/flowbite-vue.umd.cjs"}, "./index.css": "./dist/index.css"}, "scripts": {"dev": "vitepress dev docs", "start": "vitepress serve docs --port $PORT", "build": "vitepress build docs && cp -r ./docs/assets/* ./docs/.vitepress/dist/assets/", "build:package": "vite build", "build:types": "vue-tsc --declaration --emitDeclarationOnly && tsc-alias -p tsconfig.json", "build:production": "npm run build:package && npm run build:types", "lint": "eslint .", "lint-fix": "eslint . --fix", "clear": "rm -fr ./dist && rm -fr ./dist_types && rm -fr ./docs/.vitepress/dist && rm -fr ./docs/.vitepress/cache", "format": "npm run lint-fix", "test": "vitest", "coverage": "vitest run --coverage", "typecheck": "vue-tsc --noEmit", "prepublishOnly": "npm run build:production"}, "peerDependencies": {"tailwindcss": "^4", "vue": "^3.4.x"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.17.30", "@vitejs/plugin-vue": "5.2.3", "@vitest/coverage-v8": "3.1.1", "@vue/eslint-config-typescript": "14.5.0", "@vue/test-utils": "2.4.6", "@vue/tsconfig": "0.7.0", "eslint": "9.24.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vue": "10.0.0", "eslint-plugin-vue-scoped-css": "^2.9.0", "jsdom": "26.1.0", "neostandard": "^0.12.1", "tsc-alias": "1.8.15", "typescript": "5.8.3", "vite": "6.3.1", "vitepress": "1.6.3", "vitest": "3.1.1", "vue-tsc": "2.2.8"}, "dependencies": {"@tailwindcss/postcss": "^4.1.4", "@tailwindcss/vite": "^4.1.4", "@vueuse/core": "12.8.2", "@vueuse/integrations": "^13.1.0", "classnames": "2.5.1", "floating-vue": "^5.2.2", "flowbite": "3.1.2", "lodash-es": "^4.17.21", "nanoid": "5.1.5", "postcss-prefix-selector": "^2.1.1", "tailwind-merge": "3.2.0", "tailwindcss": "^4.1.4"}, "engines": {"node": ">=18.x", "npm": ">=10.x"}}